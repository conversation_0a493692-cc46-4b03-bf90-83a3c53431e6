#!/usr/bin/env node

/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

const { exec } = require('child_process');
const { promisify } = require('util');

const execAsync = promisify(exec);

const DEPENDENCIES = {
  node: {
    name: 'Node.js',
    command: 'node',
    installInstructions: 'Install Node.js from https://nodejs.org/',
  },
  npm: {
    name: 'npm',
    command: 'npm',
    installInstructions: 'npm comes with Node.js. Install Node.js from https://nodejs.org/',
  },
  npx: {
    name: 'npx',
    command: 'npx',
    installInstructions: 'npx comes with Node.js. Install Node.js from https://nodejs.org/',
  },
  uv: {
    name: 'uv (Python package manager)',
    command: 'uv',
    installInstructions: 'Install uv from https://docs.astral.sh/uv/getting-started/installation/',
  },
  uvx: {
    name: 'uvx (Python package runner)',
    command: 'uvx',
    installInstructions: 'uvx comes with uv. Install uv from https://docs.astral.sh/uv/getting-started/installation/',
  },
  git: {
    name: 'Git',
    command: 'git',
    installInstructions: 'Install Git from https://git-scm.com/',
  },
};

async function checkCommand(command) {
  try {
    const { stdout } = await execAsync(`${command} --version`, { timeout: 5000 });
    return {
      available: true,
      version: stdout.trim().split('\n')[0],
    };
  } catch (error) {
    return { available: false };
  }
}

async function checkAllDependencies() {
  console.log('🔍 Checking MCP server dependencies...\n');
  
  const results = {};
  let allAvailable = true;

  for (const [key, dep] of Object.entries(DEPENDENCIES)) {
    const check = await checkCommand(dep.command);
    results[key] = {
      ...dep,
      installed: check.available,
      version: check.version,
    };

    if (check.available) {
      console.log(`✅ ${dep.name} (${dep.command}): ${check.version}`);
    } else {
      console.log(`❌ ${dep.name} (${dep.command}): Not found`);
      allAvailable = false;
    }
  }

  console.log('\n' + '='.repeat(60));

  if (allAvailable) {
    console.log('🎉 All dependencies are available!');
    console.log('Your system is ready to run all MCP servers.');
  } else {
    console.log('⚠️  Some dependencies are missing.');
    console.log('\n📋 Installation Instructions:');
    
    for (const [key, dep] of Object.entries(results)) {
      if (!dep.installed) {
        console.log(`\n• ${dep.name}:`);
        console.log(`  ${dep.installInstructions}`);
      }
    }

    console.log('\n💡 Tip: Run "npm run install-deps" to auto-install some dependencies.');
  }

  return allAvailable;
}

async function main() {
  try {
    const success = await checkAllDependencies();
    process.exit(success ? 0 : 1);
  } catch (error) {
    console.error('Error checking dependencies:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { checkAllDependencies, checkCommand };

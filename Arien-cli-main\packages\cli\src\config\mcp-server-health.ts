/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { MCPServerConfig } from '@arien/arien-cli-core';

export enum MCPServerHealthStatus {
  HEALTHY = 'healthy',
  UNHEALTHY = 'unhealthy',
  UNKNOWN = 'unknown',
  DISABLED = 'disabled',
}

export interface MCPServerHealth {
  name: string;
  status: MCPServerHealthStatus;
  lastCheck: Date;
  consecutiveFailures: number;
  lastError?: string;
  config: MCPServerConfig;
}

export interface HealthCheckOptions {
  maxRetries: number;
  retryDelay: number;
  healthCheckInterval: number;
  disableAfterFailures: number;
}

/**
 * Monitors MCP server health and provides graceful error handling
 */
export class MCPServerHealthMonitor {
  private healthMap = new Map<string, MCPServerHealth>();
  private healthCheckIntervals = new Map<string, NodeJS.Timeout>();
  
  private readonly defaultOptions: HealthCheckOptions = {
    maxRetries: 3,
    retryDelay: 1000, // 1 second
    healthCheckInterval: 30000, // 30 seconds
    disableAfterFailures: 5,
  };

  constructor(private options: Partial<HealthCheckOptions> = {}) {
    this.options = { ...this.defaultOptions, ...options };
  }

  /**
   * Registers an MCP server for health monitoring
   */
  registerServer(name: string, config: MCPServerConfig): void {
    const health: MCPServerHealth = {
      name,
      status: MCPServerHealthStatus.UNKNOWN,
      lastCheck: new Date(),
      consecutiveFailures: 0,
      config,
    };

    this.healthMap.set(name, health);
    console.debug(`Registered MCP server '${name}' for health monitoring`);
  }

  /**
   * Updates the health status of an MCP server
   */
  updateServerHealth(
    name: string, 
    status: MCPServerHealthStatus, 
    error?: string
  ): void {
    const health = this.healthMap.get(name);
    if (!health) {
      console.warn(`Attempted to update health for unknown MCP server: ${name}`);
      return;
    }

    const previousStatus = health.status;
    health.status = status;
    health.lastCheck = new Date();

    if (status === MCPServerHealthStatus.UNHEALTHY) {
      health.consecutiveFailures++;
      health.lastError = error;
      
      // Auto-disable server after too many failures
      if (health.consecutiveFailures >= this.options.disableAfterFailures!) {
        health.status = MCPServerHealthStatus.DISABLED;
        console.warn(
          `MCP server '${name}' disabled after ${health.consecutiveFailures} consecutive failures. ` +
          `Last error: ${error}`
        );
        this.stopHealthCheck(name);
      }
    } else if (status === MCPServerHealthStatus.HEALTHY) {
      health.consecutiveFailures = 0;
      health.lastError = undefined;
      
      // Re-enable health checks if server recovered
      if (previousStatus === MCPServerHealthStatus.DISABLED) {
        console.info(`MCP server '${name}' recovered and re-enabled`);
        this.startHealthCheck(name);
      }
    }

    this.healthMap.set(name, health);
  }

  /**
   * Gets the health status of an MCP server
   */
  getServerHealth(name: string): MCPServerHealth | undefined {
    return this.healthMap.get(name);
  }

  /**
   * Gets health status of all registered servers
   */
  getAllServerHealth(): MCPServerHealth[] {
    return Array.from(this.healthMap.values());
  }

  /**
   * Gets only healthy servers that can be used
   */
  getHealthyServers(): Record<string, MCPServerConfig> {
    const healthyServers: Record<string, MCPServerConfig> = {};
    
    for (const [name, health] of this.healthMap) {
      if (health.status === MCPServerHealthStatus.HEALTHY || 
          health.status === MCPServerHealthStatus.UNKNOWN) {
        healthyServers[name] = health.config;
      }
    }
    
    return healthyServers;
  }

  /**
   * Starts periodic health checks for a server
   */
  startHealthCheck(name: string): void {
    if (this.healthCheckIntervals.has(name)) {
      return; // Already running
    }

    const interval = setInterval(() => {
      this.performHealthCheck(name);
    }, this.options.healthCheckInterval!);

    this.healthCheckIntervals.set(name, interval);
    console.debug(`Started health check for MCP server '${name}'`);
  }

  /**
   * Stops health checks for a server
   */
  stopHealthCheck(name: string): void {
    const interval = this.healthCheckIntervals.get(name);
    if (interval) {
      clearInterval(interval);
      this.healthCheckIntervals.delete(name);
      console.debug(`Stopped health check for MCP server '${name}'`);
    }
  }

  /**
   * Performs a health check on a specific server
   */
  private async performHealthCheck(name: string): Promise<void> {
    const health = this.healthMap.get(name);
    if (!health || health.status === MCPServerHealthStatus.DISABLED) {
      return;
    }

    try {
      // For now, we'll implement a basic health check
      // In the future, this could ping the MCP server or check its status
      const isHealthy = await this.checkServerConnectivity(health.config);
      
      if (isHealthy) {
        this.updateServerHealth(name, MCPServerHealthStatus.HEALTHY);
      } else {
        this.updateServerHealth(name, MCPServerHealthStatus.UNHEALTHY, 'Health check failed');
      }
    } catch (error) {
      this.updateServerHealth(name, MCPServerHealthStatus.UNHEALTHY, String(error));
    }
  }

  /**
   * Basic connectivity check for MCP server
   */
  private async checkServerConnectivity(config: MCPServerConfig): Promise<boolean> {
    // For stdio-based servers, we can't easily check without starting them
    // For now, we'll assume they're healthy if the command exists
    if (config.command) {
      try {
        const { spawn } = await import('child_process');
        const child = spawn(config.command, ['--help'], { 
          stdio: 'pipe',
          timeout: 5000 
        });
        
        return new Promise((resolve) => {
          let resolved = false;
          
          const cleanup = () => {
            if (!resolved) {
              resolved = true;
              child.kill();
            }
          };

          child.on('exit', (code) => {
            cleanup();
            resolve(code !== null); // Any exit (even error) means command exists
          });

          child.on('error', () => {
            cleanup();
            resolve(false);
          });

          // Timeout fallback
          setTimeout(() => {
            cleanup();
            resolve(false);
          }, 5000);
        });
      } catch (error) {
        return false;
      }
    }

    // For URL-based servers, we could implement HTTP health checks
    if (config.url || config.httpUrl) {
      // TODO: Implement HTTP health checks
      return true; // For now, assume healthy
    }

    return true; // Default to healthy for unknown transport types
  }

  /**
   * Attempts to recover a failed server
   */
  async recoverServer(name: string): Promise<boolean> {
    const health = this.healthMap.get(name);
    if (!health) {
      return false;
    }

    console.info(`Attempting to recover MCP server '${name}'`);

    try {
      // Reset failure count and try health check
      health.consecutiveFailures = 0;
      health.status = MCPServerHealthStatus.UNKNOWN;

      await this.performHealthCheck(name);

      const updatedHealth = this.healthMap.get(name);
      const recovered = updatedHealth?.status === MCPServerHealthStatus.HEALTHY;

      if (recovered) {
        console.info(`Successfully recovered MCP server '${name}'`);
        this.startHealthCheck(name);
      } else {
        console.warn(`Failed to recover MCP server '${name}'`);
      }

      return recovered;
    } catch (error) {
      console.error(`Error during recovery of MCP server '${name}': ${error}`);
      return false;
    }
  }

  /**
   * Cleanup all health checks
   */
  cleanup(): void {
    for (const [name] of this.healthCheckIntervals) {
      this.stopHealthCheck(name);
    }
    this.healthMap.clear();
    console.debug('MCP server health monitor cleaned up');
  }

  /**
   * Gets a summary of server health for logging/debugging
   */
  getHealthSummary(): string {
    const servers = this.getAllServerHealth();
    const healthy = servers.filter(s => s.status === MCPServerHealthStatus.HEALTHY).length;
    const unhealthy = servers.filter(s => s.status === MCPServerHealthStatus.UNHEALTHY).length;
    const disabled = servers.filter(s => s.status === MCPServerHealthStatus.DISABLED).length;
    const unknown = servers.filter(s => s.status === MCPServerHealthStatus.UNKNOWN).length;

    return `MCP Server Health: ${healthy} healthy, ${unhealthy} unhealthy, ${disabled} disabled, ${unknown} unknown (${servers.length} total)`;
  }
}
